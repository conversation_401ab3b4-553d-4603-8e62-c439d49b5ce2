#ifndef MPP_DECODER_H
#define MPP_DECODER_H

#include "common.h"

// MPP解码头文件
#include "rockchip/rk_mpi.h"
#include "rockchip/mpp_task.h"
#include "rockchip/mpp_log.h"
#include "rockchip/mpp_packet.h"
#include "rockchip/mpp_frame.h"
#include "rockchip/mpp_buffer.h"
#include "rockchip/mpp_meta.h"
#include <string.h>

#define MPP_ALIGN(x, a)         (((x)+(a)-1)&~((a)-1))

typedef struct {
    MppCtx          ctx;
    MppApi          *mpi;

    /* input and output */
    MppFrame        frame;
    /* frame buffer */
    MppBufferGroup  frm_grp;
    MppBuffer       frm_buf;

    MppBufferGroup  pkt_grp;
    MppDecCfg       cfg;
} MpiDecLoopData;

// MPP解码器接口
class MPPDecoder {
private:
    bool mpp_initialized_ = false;
    int decoder_width_ = 0;
    int decoder_height_ = 0;
    MpiDecLoopData loop_data_;

public:
    MPPDecoder() = default;
    ~MPPDecoder() { cleanup(); }

    bool init(int width, int height, int32_t format);
    bool decode_mjpeg(const Frame& src, Frame& dst);
    void cleanup();

private:
    bool init_mpp_decoder(int width, int height);
    bool get_decoded_frame(Frame& dst);
    int decode_packet_internal(void* buf, size_t len);
    void decoder_deinit_internal();
    int decoder_init_internal(int width, int height, MppCodingType type, MppFrameFormat format);
};

// MPPDecoder实现
inline bool MPPDecoder::init(int width, int height, int32_t format) {
    if (format == V4L2_PIX_FMT_MJPEG) {
        return init_mpp_decoder(width, height);
    }
    LOG_W("MPP decoder not available or unsupported format");
    return false;
}

inline bool MPPDecoder::init_mpp_decoder(int width, int height) {
    memset(&loop_data_, 0, sizeof(loop_data_));
    int ret = decoder_init_internal(width, height, MPP_VIDEO_CodingMJPEG, MPP_FMT_YUV420SP);
    if (ret != 0) {
        LOG_E("Failed to initialize MPP MJPEG decoder: %d", ret);
        return false;
    }

    mpp_initialized_ = true;
    decoder_width_ = width;
    decoder_height_ = height;
    LOG_I("MPP MJPEG decoder initialized: %dx%d", width, height);
    return true;
}

inline bool MPPDecoder::decode_mjpeg(const Frame& src, Frame& dst) {
    if (!mpp_initialized_) {
        // 尝试初始化解码器
        if (!init_mpp_decoder(src.width, src.height)) {
            return false;
        }
    }

    // 调用MPP解码函数
    int ret = decode_packet_internal((void*)src.data.data(), src.data.size());
    if (ret != 0) {
        LOG_E("MPP MJPEG decode failed: %d", ret);
        return false;
    }

    // 获取解码后的帧数据
    return get_decoded_frame(dst);
}

inline bool MPPDecoder::get_decoded_frame(Frame& dst) {
    MppFrame frame = loop_data_.frame;
    if (!frame) {
        LOG_E("MPP frame is null");
        return false;
    }

    MppBuffer buffer = mpp_frame_get_buffer(frame);
    if (!buffer) {
        LOG_E("MPP frame buffer is null");
        return false;
    }

    dst.width = mpp_frame_get_width(frame);
    dst.height = mpp_frame_get_height(frame);
    dst.format = V4L2_PIX_FMT_NV12;  // MPP通常输出NV12格式

    // 获取实际的解码数据
    void* ptr = mpp_buffer_get_ptr(buffer);
    size_t size = mpp_buffer_get_size(buffer);

    dst.data.resize(size);
    memcpy(dst.data.data(), ptr, size);

    LOG_D("MPP decoded frame: %dx%d, format=0x%08x, size=%zu",
          dst.width, dst.height, dst.format, dst.data.size());

    return true;
}

inline void MPPDecoder::cleanup() {
    if (mpp_initialized_) {
        decoder_deinit_internal();
        mpp_initialized_ = false;
    }
}

// 从 mpp_decode.cpp 移植的内部实现
inline int MPPDecoder::decode_packet_internal(void* buf, size_t len) {
    MpiDecLoopData *data = &loop_data_;
    MPP_RET ret = MPP_OK;
    MppCtx ctx  = data->ctx;
    MppApi *mpi = data->mpi;
    MppFrame  frame  = data->frame;
    MppTask task = NULL;
    MppPacket mpkt = NULL;
    MppBuffer mbuf;

    mpp_buffer_get(data->pkt_grp, &mbuf, len);
    if (!mbuf) {
        mpp_log("%p get packet MppBuffer failed\n");
        return -1;
    }
    /* FIXME: performance bad */
    memcpy(mpp_buffer_get_ptr(mbuf), buf, len);

    mpp_packet_init_with_buffer(&mpkt, mbuf);
    mpp_buffer_put(mbuf);
    if (!mpkt) {
        return -1;
    }

    mpp_packet_set_size(mpkt, len);
    mpp_packet_set_length(mpkt, len);

    ret = mpi->poll(ctx, MPP_PORT_INPUT, MPP_POLL_BLOCK);
    if (ret) {
        mpp_err("%p mpp input poll failed\n", ctx);
        goto decode_out;
    }

    ret = mpi->dequeue(ctx, MPP_PORT_INPUT, &task);  /* input queue */
    if (ret) {
        mpp_err("%p mpp task input dequeue failed\n", ctx);
        goto decode_out;
    }

    mpp_task_meta_set_packet(task, KEY_INPUT_PACKET, mpkt);
    mpp_task_meta_set_frame(task, KEY_OUTPUT_FRAME, frame);

    ret = mpi->enqueue(ctx, MPP_PORT_INPUT, task);  /* input queue */
    if (ret) {
        mpp_err("%p mpp task input enqueue failed\n", ctx);
        goto decode_out;
    }

    /* poll and wait here */
    ret = mpi->poll(ctx, MPP_PORT_OUTPUT, MPP_POLL_BLOCK);
    if (ret) {
        mpp_err("%p mpp output poll failed\n", ctx);
        goto decode_out;
    }

    ret = mpi->dequeue(ctx, MPP_PORT_OUTPUT, &task); /* output queue */
    if (ret) {
        mpp_err("%p mpp task output dequeue failed\n", ctx);
        goto decode_out;
    }

    /* output queue */
    ret = mpi->enqueue(ctx, MPP_PORT_OUTPUT, task);
    if (ret)
        mpp_err("%p mpp task output enqueue failed\n", ctx);

decode_out:
    if (mpkt) {
        mpp_packet_deinit(&mpkt);
    }

    return ret;
}

inline void MPPDecoder::decoder_deinit_internal() {
    MpiDecLoopData* loop_data = &loop_data_;
    if (loop_data->mpi && loop_data->ctx) {
        loop_data->mpi->reset(loop_data->ctx);
    }

    if (loop_data->frame) {
        mpp_frame_deinit(&loop_data->frame);
        loop_data->frame = NULL;
    }

    if (loop_data->ctx) {
        mpp_destroy(loop_data->ctx);
        loop_data->ctx = NULL;
    }

    if (loop_data->frm_buf) {
        mpp_buffer_put(loop_data->frm_buf);
        loop_data->frm_buf = NULL;
    }

    if (loop_data->frm_grp) {
        mpp_buffer_group_put(loop_data->frm_grp);
        loop_data->frm_grp = NULL;
    }

    if (loop_data->pkt_grp) {
        mpp_buffer_group_put(loop_data->pkt_grp);
        loop_data->pkt_grp = NULL;
    }

    if (loop_data->cfg) {
        mpp_dec_cfg_deinit(loop_data->cfg);
        loop_data->cfg = NULL;
    }
}

inline int MPPDecoder::decoder_init_internal(int width, int height, MppCodingType type, MppFrameFormat format) {
    // base flow context
    MppCtx ctx          = NULL;
    MppApi *mpi         = NULL;

    // input / output
    MppFrame  frame     = NULL;

    // config for runtime mode
    RK_U32 need_split   = 1;

    // resources
    MppBuffer frm_buf   = NULL;
    MPP_RET ret = MPP_OK;
    MpiDecLoopData* data = &loop_data_;
    MppFrameFormat set_format = format;

    mpp_log("mpi_dec start\n");

    RK_U32 hor_stride = MPP_ALIGN(width, 16);
    RK_U32 ver_stride = MPP_ALIGN(height, 16);

    ret = mpp_buffer_group_get_internal(&data->frm_grp, MPP_BUFFER_TYPE_ION);
    if (ret) {
        mpp_err("failed to get buffer group for out frame ret %d\n", ret);
        goto MPP_TEST_OUT;
    }

    ret = mpp_buffer_group_get_internal(&data->pkt_grp, MPP_BUFFER_TYPE_DRM);
    if (ret) {
        mpp_err("failed to get pkt group for input frame ret %d\n", ret);
        goto MPP_TEST_OUT;
    }

    ret = mpp_frame_init(&frame); /* output frame */
    if (ret) {
        mpp_err("mpp_frame_init failed\n");
        goto MPP_TEST_OUT;
    }
    data->frame = frame;

    /*
     * NOTE: For jpeg could have YUV420 and YUV422 the buffer should be
     * larger for output. And the buffer dimension should align to 16.
     * YUV420 buffer is 3/2 times of w*h.
     * YUV422 buffer is 2 times of w*h.
     * So create larger buffer with 2 times w*h.
      */
    ret = mpp_buffer_get(data->frm_grp, &frm_buf, hor_stride * ver_stride * 4);
    if (ret) {
        mpp_err("failed to get buffer for input frame ret %d\n", ret);
        goto MPP_TEST_OUT;
    }
    data->frm_buf = frm_buf;
    mpp_frame_set_buffer(frame, frm_buf);

    // decoder demo
    ret = mpp_create(&ctx, &mpi);
    if (ret) {
        mpp_err("mpp_create failed\n");
        goto MPP_TEST_OUT;
    }
    data->ctx = ctx;
    data->mpi = mpi;

    mpp_log("%p mpi_dec decoder start w %d h %d type %d\n",
            ctx, width, height, type);

    // ret = mpi->control(ctx, MPP_SET_DISABLE_THREAD, NULL);
    ret = mpp_init(ctx, MPP_CTX_DEC, type);
    if (ret) {
        mpp_err("%p mpp_init failed\n", ctx);
        goto MPP_TEST_OUT;
    }

    mpp_dec_cfg_init(&data->cfg);

    /* get default config from decoder context */
    ret = mpi->control(ctx, MPP_DEC_GET_CFG, data->cfg);
    if (ret) {
        mpp_err("%p failed to get decoder cfg ret %d\n", ctx, ret);
        goto MPP_TEST_OUT;
    }

    /*
     * split_parse is to enable mpp internal frame spliter when the input
     * packet is not aplited into frames.
     */
    ret = mpp_dec_cfg_set_u32(data->cfg, "base:split_parse", need_split);
    if (ret) {
        mpp_err("%p failed to set split_parse ret %d\n", ctx, ret);
        goto MPP_TEST_OUT;
    }

    ret = mpi->control(ctx, MPP_DEC_SET_CFG, data->cfg);
    if (ret) {
        mpp_err("%p failed to set cfg %p ret %d\n", ctx, data->cfg, ret);
        goto MPP_TEST_OUT;
    }

    ret = mpi->control(ctx, MPP_DEC_SET_OUTPUT_FORMAT, &set_format);
    if (ret) {
        mpp_err("%p failed to set format to %d ret %d\n", ctx, set_format, ret);
        goto MPP_TEST_OUT;
    }
    mpp_log("%p mpi_dec decoder init success.\n");
    return ret;

MPP_TEST_OUT:
    decoder_deinit_internal();
    return ret;
}

#endif // MPP_DECODER_H
