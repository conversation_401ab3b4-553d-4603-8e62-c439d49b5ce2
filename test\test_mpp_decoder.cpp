#include "mpp_decoder.h"
#include "common.h"
#include <iostream>
#include <chrono>
#include <vector>
#include <fstream>

// 测试用的MJPEG帧数据生成
void generate_test_mjpeg_frame(Frame& frame, int width, int height) {
    frame.width = width;
    frame.height = height;
    frame.format = V4L2_PIX_FMT_MJPEG;
    frame.frame_id = 1;
    frame.timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::steady_clock::now().time_since_epoch()).count();
    frame.source_type = 0;
    frame.is_keyframe = true;
    frame.valid = true;
    
    // 生成一个简单的MJPEG头部（用于测试）
    // 注意：这不是真正的MJPEG数据，只是用于测试API调用
    std::vector<uint8_t> mjpeg_header = {
        0xFF, 0xD8, 0xFF, 0xE0,  // JPEG SOI + APP0
        0x00, 0x10,              // APP0 length
        0x4A, 0x46, 0x49, 0x46, 0x00,  // "JFIF\0"
        0x01, 0x01,              // Version 1.1
        0x01,                    // Units (dots per inch)
        0x00, 0x48, 0x00, 0x48,  // X and Y density (72 DPI)
        0x00, 0x00               // Thumbnail width and height
    };
    
    frame.data = mjpeg_header;
    // 添加一些虚拟数据
    frame.data.resize(1024);
    for (size_t i = mjpeg_header.size(); i < frame.data.size(); i++) {
        frame.data[i] = (i % 256);
    }
}

// 打印帧信息
void print_frame_info(const Frame& frame, const std::string& name) {
    std::cout << name << " Frame Info:" << std::endl;
    std::cout << "  Size: " << frame.width << "x" << frame.height << std::endl;
    std::cout << "  Format: 0x" << std::hex << frame.format << std::dec << std::endl;
    std::cout << "  Data size: " << frame.data.size() << " bytes" << std::endl;
    std::cout << "  Valid: " << (frame.valid ? "true" : "false") << std::endl;
    std::cout << "  Keyframe: " << (frame.is_keyframe ? "true" : "false") << std::endl;
    std::cout << std::endl;
}

// 测试基本初始化
bool test_mpp_decoder_init() {
    std::cout << "=== Testing MPP Decoder Initialization ===" << std::endl;
    
    MPPDecoder decoder;
    
    // 测试MJPEG格式初始化
    bool result = decoder.init(1280, 720, V4L2_PIX_FMT_MJPEG);
    
    if (result) {
        std::cout << "✓ MPP decoder initialization successful for MJPEG 1280x720" << std::endl;
    } else {
        std::cout << "✗ MPP decoder initialization failed for MJPEG 1280x720" << std::endl;
    }
    
    // 测试不支持的格式
    bool unsupported_result = decoder.init(1920, 1080, V4L2_PIX_FMT_YUYV);
    if (!unsupported_result) {
        std::cout << "✓ MPP decoder correctly rejected unsupported format (YUYV)" << std::endl;
    } else {
        std::cout << "✗ MPP decoder should have rejected unsupported format" << std::endl;
    }
    
    decoder.cleanup();
    std::cout << std::endl;
    return result;
}

// 测试MJPEG解码
bool test_mjpeg_decode() {
    std::cout << "=== Testing MJPEG Decode ===" << std::endl;
    
    MPPDecoder decoder;
    
    // 初始化解码器
    if (!decoder.init(640, 480, V4L2_PIX_FMT_MJPEG)) {
        std::cout << "✗ Failed to initialize decoder for MJPEG decode test" << std::endl;
        return false;
    }
    
    // 生成测试MJPEG帧
    Frame src_frame, dst_frame;
    generate_test_mjpeg_frame(src_frame, 640, 480);
    
    std::cout << "Generated test MJPEG frame:" << std::endl;
    print_frame_info(src_frame, "Source");
    
    // 尝试解码
    bool decode_result = decoder.decode_mjpeg(src_frame, dst_frame);
    
    if (decode_result) {
        std::cout << "✓ MJPEG decode completed successfully" << std::endl;
        print_frame_info(dst_frame, "Decoded");
        
        // 验证输出格式应该是NV12
        if (dst_frame.format == V4L2_PIX_FMT_NV12) {
            std::cout << "✓ Output format is NV12 as expected" << std::endl;
        } else {
            std::cout << "✗ Unexpected output format: 0x" << std::hex << dst_frame.format << std::dec << std::endl;
        }
    } else {
        std::cout << "✗ MJPEG decode failed (expected on systems without MPP hardware)" << std::endl;
    }
    
    decoder.cleanup();
    std::cout << std::endl;
    return decode_result;
}

// 测试多次初始化和清理
bool test_multiple_init_cleanup() {
    std::cout << "=== Testing Multiple Init/Cleanup Cycles ===" << std::endl;
    
    MPPDecoder decoder;
    bool all_passed = true;
    
    for (int i = 0; i < 3; i++) {
        std::cout << "Cycle " << (i + 1) << ":" << std::endl;
        
        bool init_result = decoder.init(1920, 1080, V4L2_PIX_FMT_MJPEG);
        if (init_result) {
            std::cout << "  ✓ Init successful" << std::endl;
        } else {
            std::cout << "  ✗ Init failed" << std::endl;
            all_passed = false;
        }
        
        decoder.cleanup();
        std::cout << "  ✓ Cleanup completed" << std::endl;
    }
    
    if (all_passed) {
        std::cout << "✓ All init/cleanup cycles completed successfully" << std::endl;
    } else {
        std::cout << "✗ Some init/cleanup cycles failed" << std::endl;
    }
    
    std::cout << std::endl;
    return all_passed;
}

// 测试不同分辨率
bool test_different_resolutions() {
    std::cout << "=== Testing Different Resolutions ===" << std::endl;
    
    MPPDecoder decoder;
    bool all_passed = true;
    
    std::vector<std::pair<int, int>> resolutions = {
        {320, 240},   // QVGA
        {640, 480},   // VGA
        {1280, 720},  // HD
        {1920, 1080}, // Full HD
        {3840, 2160}  // 4K
    };
    
    for (const auto& res : resolutions) {
        std::cout << "Testing resolution: " << res.first << "x" << res.second << std::endl;
        
        bool result = decoder.init(res.first, res.second, V4L2_PIX_FMT_MJPEG);
        if (result) {
            std::cout << "  ✓ Init successful for " << res.first << "x" << res.second << std::endl;
        } else {
            std::cout << "  ✗ Init failed for " << res.first << "x" << res.second << std::endl;
            all_passed = false;
        }
        
        decoder.cleanup();
    }
    
    std::cout << std::endl;
    return all_passed;
}

// 主测试函数
int main() {
    std::cout << "MPP Decoder Test Suite" << std::endl;
    std::cout << "======================" << std::endl;
    std::cout << std::endl;
    
    bool all_tests_passed = true;
    
    // 运行所有测试
    all_tests_passed &= test_mpp_decoder_init();
    all_tests_passed &= test_mjpeg_decode();
    all_tests_passed &= test_multiple_init_cleanup();
    all_tests_passed &= test_different_resolutions();
    
    // 输出总结
    std::cout << "=== Test Summary ===" << std::endl;
    if (all_tests_passed) {
        std::cout << "✓ All tests passed!" << std::endl;
        return 0;
    } else {
        std::cout << "✗ Some tests failed. Note: Failures are expected on systems without MPP hardware." << std::endl;
        return 1;
    }
}
