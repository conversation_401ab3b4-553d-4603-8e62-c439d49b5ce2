#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "mpp_decoder.h"
#include "common.h"
#include <chrono>
#include <vector>

// Test fixture for MPP Decoder tests
class MPPDecoderTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Setup test environment
        decoder_ = std::make_unique<MPPDecoder>();
    }
    
    void TearDown() override {
        // Cleanup test environment
        if (decoder_) {
            decoder_->cleanup();
        }
    }
    
    // Helper function to generate test MJPEG frame
    void generateTestMJPEGFrame(Frame& frame, int width, int height) {
        frame.width = width;
        frame.height = height;
        frame.format = V4L2_PIX_FMT_MJPEG;
        frame.frame_id = 1;
        frame.timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::steady_clock::now().time_since_epoch()).count();
        frame.source_type = 0;
        frame.is_keyframe = true;
        frame.valid = true;
        
        // Generate a simple MJPEG header (for testing purposes)
        std::vector<uint8_t> mjpeg_header = {
            0xFF, 0xD8, 0xFF, 0xE0,  // JPEG SOI + APP0
            0x00, 0x10,              // APP0 length
            0x4A, 0x46, 0x49, 0x46, 0x00,  // "JFIF\0"
            0x01, 0x01,              // Version 1.1
            0x01,                    // Units (dots per inch)
            0x00, 0x48, 0x00, 0x48,  // X and Y density (72 DPI)
            0x00, 0x00               // Thumbnail width and height
        };
        
        frame.data = mjpeg_header;
        // Add some dummy data
        frame.data.resize(1024);
        for (size_t i = mjpeg_header.size(); i < frame.data.size(); i++) {
            frame.data[i] = (i % 256);
        }
    }
    
    std::unique_ptr<MPPDecoder> decoder_;
};

// Test MPP Decoder initialization with MJPEG format
TEST_F(MPPDecoderTest, InitializationMJPEGFormat) {
    // Test successful initialization with MJPEG format
    bool result = decoder_->init(1280, 720, V4L2_PIX_FMT_MJPEG);
    
    // Note: This may fail on systems without MPP hardware, which is expected
    if (result) {
        EXPECT_TRUE(result);
        GTEST_LOG_(INFO) << "MPP decoder initialized successfully for MJPEG 1280x720";
    } else {
        GTEST_LOG_(WARNING) << "MPP decoder initialization failed (expected on systems without MPP hardware)";
    }
}

// Test MPP Decoder initialization with unsupported format
TEST_F(MPPDecoderTest, InitializationUnsupportedFormat) {
    // Test initialization with unsupported format (should fail)
    bool result = decoder_->init(1920, 1080, V4L2_PIX_FMT_YUYV);
    
    EXPECT_FALSE(result);
    GTEST_LOG_(INFO) << "MPP decoder correctly rejected unsupported format (YUYV)";
}

// Test MPP Decoder initialization with different resolutions
TEST_F(MPPDecoderTest, InitializationDifferentResolutions) {
    std::vector<std::pair<int, int>> resolutions = {
        {320, 240},   // QVGA
        {640, 480},   // VGA
        {1280, 720},  // HD
        {1920, 1080}, // Full HD
    };
    
    for (const auto& res : resolutions) {
        // Cleanup previous initialization
        decoder_->cleanup();
        
        bool result = decoder_->init(res.first, res.second, V4L2_PIX_FMT_MJPEG);
        
        if (result) {
            EXPECT_TRUE(result);
            GTEST_LOG_(INFO) << "Init successful for " << res.first << "x" << res.second;
        } else {
            GTEST_LOG_(WARNING) << "Init failed for " << res.first << "x" << res.second 
                               << " (expected on systems without MPP hardware)";
        }
    }
}

// Test MJPEG decoding functionality
TEST_F(MPPDecoderTest, MJPEGDecoding) {
    // Initialize decoder
    bool init_result = decoder_->init(640, 480, V4L2_PIX_FMT_MJPEG);
    
    if (!init_result) {
        GTEST_SKIP() << "Skipping decode test - decoder initialization failed (no MPP hardware)";
        return;
    }
    
    // Generate test MJPEG frame
    Frame src_frame, dst_frame;
    generateTestMJPEGFrame(src_frame, 640, 480);
    
    // Verify source frame properties
    EXPECT_EQ(src_frame.width, 640);
    EXPECT_EQ(src_frame.height, 480);
    EXPECT_EQ(src_frame.format, V4L2_PIX_FMT_MJPEG);
    EXPECT_TRUE(src_frame.is_keyframe);
    EXPECT_GT(src_frame.data.size(), 0);
    
    // Attempt to decode
    bool decode_result = decoder_->decode_mjpeg(src_frame, dst_frame);
    
    if (decode_result) {
        // Verify decoded frame properties
        EXPECT_GT(dst_frame.width, 0);
        EXPECT_GT(dst_frame.height, 0);
        EXPECT_EQ(dst_frame.format, V4L2_PIX_FMT_NV12);  // MPP typically outputs NV12
        EXPECT_GT(dst_frame.data.size(), 0);
        
        GTEST_LOG_(INFO) << "MJPEG decode successful - Output: " 
                        << dst_frame.width << "x" << dst_frame.height 
                        << ", format: 0x" << std::hex << dst_frame.format;
    } else {
        GTEST_LOG_(WARNING) << "MJPEG decode failed (expected on systems without MPP hardware)";
    }
}

// Test multiple initialization and cleanup cycles
TEST_F(MPPDecoderTest, MultipleInitCleanupCycles) {
    const int num_cycles = 3;
    
    for (int i = 0; i < num_cycles; i++) {
        bool init_result = decoder_->init(1920, 1080, V4L2_PIX_FMT_MJPEG);
        
        if (init_result) {
            EXPECT_TRUE(init_result);
            GTEST_LOG_(INFO) << "Cycle " << (i + 1) << ": Init successful";
        } else {
            GTEST_LOG_(WARNING) << "Cycle " << (i + 1) << ": Init failed (expected on systems without MPP hardware)";
        }
        
        // Cleanup should not throw or crash
        EXPECT_NO_THROW(decoder_->cleanup());
    }
}

// Test decoder behavior with invalid input
TEST_F(MPPDecoderTest, InvalidInputHandling) {
    // Try to decode without initialization
    Frame src_frame, dst_frame;
    generateTestMJPEGFrame(src_frame, 640, 480);
    
    bool decode_result = decoder_->decode_mjpeg(src_frame, dst_frame);
    
    // Should handle gracefully (may auto-initialize or return false)
    if (!decode_result) {
        GTEST_LOG_(INFO) << "Decoder correctly handled decode attempt without initialization";
    }
    
    // Test with empty frame data
    Frame empty_frame;
    empty_frame.width = 640;
    empty_frame.height = 480;
    empty_frame.format = V4L2_PIX_FMT_MJPEG;
    empty_frame.data.clear();  // Empty data
    
    bool empty_decode_result = decoder_->decode_mjpeg(empty_frame, dst_frame);
    
    // Should handle empty data gracefully
    EXPECT_FALSE(empty_decode_result);
    GTEST_LOG_(INFO) << "Decoder correctly handled empty frame data";
}

// Test cleanup without initialization
TEST_F(MPPDecoderTest, CleanupWithoutInit) {
    // Create a fresh decoder
    MPPDecoder test_decoder;
    
    // Cleanup should not crash even without initialization
    EXPECT_NO_THROW(test_decoder.cleanup());
    GTEST_LOG_(INFO) << "Cleanup without initialization handled gracefully";
}

// Performance test (basic timing)
TEST_F(MPPDecoderTest, BasicPerformanceTest) {
    bool init_result = decoder_->init(1280, 720, V4L2_PIX_FMT_MJPEG);
    
    if (!init_result) {
        GTEST_SKIP() << "Skipping performance test - decoder initialization failed";
        return;
    }
    
    Frame src_frame, dst_frame;
    generateTestMJPEGFrame(src_frame, 1280, 720);
    
    const int num_iterations = 10;
    auto start_time = std::chrono::high_resolution_clock::now();
    
    int successful_decodes = 0;
    for (int i = 0; i < num_iterations; i++) {
        if (decoder_->decode_mjpeg(src_frame, dst_frame)) {
            successful_decodes++;
        }
    }
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    
    if (successful_decodes > 0) {
        double avg_time = static_cast<double>(duration.count()) / successful_decodes;
        GTEST_LOG_(INFO) << "Performance test: " << successful_decodes << "/" << num_iterations 
                        << " successful decodes, average time: " << avg_time << "ms";
        
        // Basic performance expectation (should be reasonably fast)
        EXPECT_LT(avg_time, 1000.0);  // Should be less than 1 second per decode
    } else {
        GTEST_LOG_(WARNING) << "No successful decodes in performance test";
    }
}
