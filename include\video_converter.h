#ifndef VIDEO_CONVERTER_H
#define VIDEO_CONVERTER_H

#include "common.h"
#include <atomic>
#include <thread>
#include "thread_pool.h"
#include "rga_accelerator.h"
#include "mpp_decoder.h"
#include "gstreamer_codec.h"

class VideoConverter {
private:
    std::atomic<bool> stop_requested_{false};
    std::atomic<bool> running_{false};
    std::thread converter_thread_;
    ThreadPool thread_pool_{2};

    // DDS接口
    std::unique_ptr<DDSVideoReader> input_reader_;
    std::unique_ptr<DDSVideoWriter> ai_writer_;
    std::unique_ptr<DDSVideoWriter> cloud_writer_;

    // 硬件加速组件
    std::unique_ptr<RGAAccelerator> rga_accel_;
    std::unique_ptr<MPPDecoder> mpp_decoder_;
    std::unique_ptr<GStreamerCodec> gst_codec_;

    // 配置参数
    VideoConverterConfig config_;

    // 统计信息
    std::atomic<uint64_t> frames_processed_{0};
    std::atomic<uint64_t> frames_dropped_{0};
    std::atomic<uint64_t> ai_frames_sent_{0};
    std::atomic<uint64_t> cloud_frames_sent_{0};

    // 性能监控
    CPUMonitor cpu_monitor_;

public:
    VideoConverter() = default;
    ~VideoConverter() { stop(); }

    void set_config(const VideoConverterConfig& config) {
        config_ = config;
    }

    bool init(const std::string& input_topic = "Video_Frames",
              const std::string& ai_topic = "AI_Frames",
              const std::string& cloud_topic = "Cloud_Frames") {
        // 创建DDS读写器
        input_reader_ = std::make_unique<DDSVideoReader>(input_topic, 5);
        if (!input_reader_) {
            LOG_E("Failed to initialize input reader");
            return false;
        }

        // 只在启用时创建AI writer
        if (config_.enable_ai) {
            ai_writer_ = std::make_unique<DDSVideoWriter>(ai_topic, 5);
            if (!ai_writer_) {
                LOG_E("Failed to initialize AI writer");
                return false;
            }
            LOG_I("AI writer initialized for topic: %s", ai_topic.c_str());
        } else {
            LOG_I("AI processing disabled, skipping AI writer initialization");
        }

        // 只在启用时创建Cloud writer
        if (config_.enable_cloud_streaming) {
            cloud_writer_ = std::make_unique<DDSVideoWriter>(cloud_topic, 5);
            if (!cloud_writer_) {
                LOG_E("Failed to initialize cloud writer");
                return false;
            }
            LOG_I("Cloud writer initialized for topic: %s", cloud_topic.c_str());
        } else {
            LOG_I("Cloud streaming disabled, skipping cloud writer initialization");
        }

        // 初始化硬件加速组件
        if (config_.enable_hardware_acceleration) {
            rga_accel_ = std::make_unique<RGAAccelerator>();
            if (!rga_accel_->init()) {
                LOG_W("RGA acceleration not available");
            }

            mpp_decoder_ = std::make_unique<MPPDecoder>();
            // MPP decoder will be initialized when needed

            gst_codec_ = std::make_unique<GStreamerCodec>();
            if (!gst_codec_->init()) {
                LOG_W("GStreamer codec not available");
            }
        }

        LOG_I("Video converter initialized");
        return true;
    }

    void start() {
        if (running_.load()) {
            LOG_W("Video converter already running");
            return;
        }

        stop_requested_.store(false);
        converter_thread_ = std::thread(&VideoConverter::run, this);
        running_.store(true);
        LOG_I("Video converter started");
    }

    void stop() {
        if (!running_.load()) {
            return;
        }

        stop_requested_.store(true);
        thread_pool_.shutdown();
        if (converter_thread_.joinable()) {
            converter_thread_.join();
        }
        running_.store(false);

        LOG_I("Video converter stopped");
    }

    void run() {
        LOG_I("Video converter thread started");

        while (!stop_requested_.load()) {
            try {
                Frame input_frame;
                // 从DDS读取数据
                if (input_reader_->read(input_frame, 100)) {  // 100ms超时
                    process_frame(input_frame);
                    frames_processed_.fetch_add(1);
                }
            } catch (const std::exception& e) {
                LOG_E("Converter loop exception: %s", e.what());
                frames_dropped_.fetch_add(1);
                std::this_thread::sleep_for(std::chrono::milliseconds(100));
            }
        }

        LOG_I("Video converter thread stopped");
    }

    // 获取统计信息
    struct Stats {
        uint64_t frames_processed;
        uint64_t frames_dropped;
        uint64_t ai_frames_sent;
        uint64_t cloud_frames_sent;
        float cpu_usage;
    };

    void get_stats(Stats& stats) {
        stats.frames_processed = frames_processed_.load();
        stats.frames_dropped = frames_dropped_.load();
        stats.ai_frames_sent = ai_frames_sent_.load();
        stats.cloud_frames_sent = cloud_frames_sent_.load();
        stats.cpu_usage = cpu_monitor_.get_usage();
    }

    // 信号处理
    void handle_signal(int signal) {
        if (signal == SIGUSR1) {
            // 清理缓存，释放内存
            LOG_I("Signal SIGUSR1: cleaning up memory");
            // 这里可以添加内存清理逻辑
        }
    }

private:
    void process_frame(const Frame& input_frame) {
        // 根据配置决定是否处理AI和云端流媒体
        std::vector<std::future<void>> tasks;

        if (config_.enable_ai) {
            auto ai_task = thread_pool_.submit([this, input_frame] {
                try {
                    convert_for_ai(input_frame);
                } catch (const std::exception& e) {
                    LOG_E("AI conversion exception: %s", e.what());
                    frames_dropped_.fetch_add(1);
                }
            });
            tasks.push_back(std::move(ai_task));
        }

        if (config_.enable_cloud_streaming) {
            auto cloud_task = thread_pool_.submit([this, input_frame] {
                try {
                    convert_for_cloud(input_frame);
                } catch (const std::exception& e) {
                    LOG_E("Cloud conversion exception: %s", e.what());
                    frames_dropped_.fetch_add(1);
                }
            });
            tasks.push_back(std::move(cloud_task));
        }

        // 等待所有任务完成
        for (auto& task : tasks) {
            task.get();
        }

        // 如果两个处理都被禁用，记录警告
        if (!config_.enable_ai && !config_.enable_cloud_streaming) {
            LOG_W("Both AI and cloud streaming are disabled, frame %lu will be dropped", input_frame.frame_id);
            frames_dropped_.fetch_add(1);
        }
    }

    void convert_for_ai(const Frame& src) {
        Frame dst;
        dst.frame_id = src.frame_id;
        dst.timestamp = src.timestamp;
        dst.width = config_.ai_width;
        dst.height = config_.ai_height;
        dst.format = V4L2_PIX_FMT_RGB24;  // RGB888格式
        dst.source_type = src.source_type;
        dst.is_keyframe = src.is_keyframe;

        bool success = false;

        // 根据输入格式选择处理路径
        switch (src.format) {
            case V4L2_PIX_FMT_YUYV:
            case V4L2_PIX_FMT_UYVY:
            case V4L2_PIX_FMT_RGB24:
            case V4L2_PIX_FMT_BGR24:
                // YUV/RGB格式：通过RGA转换和缩放到640x640 RGB888
                success = convert_yuv_rgb_for_ai(src, dst);
                break;
                
            case V4L2_PIX_FMT_MJPEG:
                // MJPEG格式：MPP解码后RGA缩放
                success = convert_mjpeg_for_ai(src, dst);
                break;
                
            case V4L2_PIX_FMT_H264:
            case V4L2_PIX_FMT_H265:
                // H264/H265格式：GStreamer解码后RGA缩放
                success = convert_h264_h265_for_ai(src, dst);
                break;
                
            default:
                LOG_E("Unsupported input format for AI: 0x%08x", src.format);
                return;
        }

        if (success) {
            dst.valid = true;
            if (ai_writer_ && ai_writer_->write(dst)) {
                ai_frames_sent_.fetch_add(1);
            } else if (ai_writer_) {
                LOG_W("Failed to send frame %lu to AI processor", dst.frame_id);
                frames_dropped_.fetch_add(1);
            } else {
                // AI writer不存在，这是正常情况（AI被禁用）
                LOG_D("AI processing disabled, frame %lu not sent to AI", dst.frame_id);
            }
        } else {
            LOG_E("Failed to convert frame %lu for AI", src.frame_id);
            frames_dropped_.fetch_add(1);
        }
    }

    void convert_for_cloud(const Frame& src) {
        Frame dst;
        dst.frame_id = src.frame_id;
        dst.timestamp = src.timestamp;
        dst.source_type = src.source_type;
        dst.is_keyframe = src.is_keyframe;

        bool success = false;

        // 根据输入格式选择处理路径
        switch (src.format) {
            case V4L2_PIX_FMT_H264:
            case V4L2_PIX_FMT_H265:
                // H264/H265格式：无需转换直接给cloud streaming
                success = convert_h264_h265_for_cloud(src, dst);
                break;
                
            case V4L2_PIX_FMT_YUYV:
            case V4L2_PIX_FMT_UYVY:
            case V4L2_PIX_FMT_RGB24:
            case V4L2_PIX_FMT_BGR24:
                // YUV/RGB格式：通过GStreamer appsrc编码
                success = convert_yuv_rgb_for_cloud(src, dst);
                break;
                
            case V4L2_PIX_FMT_MJPEG:
                // MJPEG格式：通过GStreamer appsrc编码
                success = convert_mjpeg_for_cloud(src, dst);
                break;
                
            default:
                LOG_E("Unsupported input format for cloud: 0x%08x", src.format);
                return;
        }

        if (success) {
            dst.valid = true;
            if (cloud_writer_ && cloud_writer_->write(dst)) {
                cloud_frames_sent_.fetch_add(1);
            } else if (cloud_writer_) {
                LOG_W("Failed to send frame %lu to cloud streamer", dst.frame_id);
                frames_dropped_.fetch_add(1);
            } else {
                // Cloud writer不存在，这是正常情况（云端流媒体被禁用）
                LOG_D("Cloud streaming disabled, frame %lu not sent to cloud", dst.frame_id);
            }
        } else {
            LOG_E("Failed to convert frame %lu for cloud", src.frame_id);
            frames_dropped_.fetch_add(1);
        }
    }

    // AI路径的具体转换方法
    bool convert_yuv_rgb_for_ai(const Frame& src, Frame& dst) {
        // YUV/RGB格式通过RGA转换和缩放到640x640 RGB888
        if (rga_accel_) {
            return rga_accel_->convert_and_scale(src, dst, config_.ai_width, config_.ai_height, V4L2_PIX_FMT_RGB24);
        }

        // 软件fallback
        return software_convert_for_ai(src, dst);
    }

    bool convert_mjpeg_for_ai(const Frame& src, Frame& dst) {
        // MJPEG格式通过MPP解码后，通过RGA缩放到640x640 RGB888
        Frame decoded_frame;

        // 1. MPP解码MJPEG
        if (!mpp_decoder_ || !mpp_decoder_->decode_mjpeg(src, decoded_frame)) {
            LOG_E("Failed to decode MJPEG frame");
            return false;
        }

        // 2. RGA缩放到目标尺寸和格式
        if (rga_accel_) {
            return rga_accel_->convert_and_scale(decoded_frame, dst, config_.ai_width, config_.ai_height, V4L2_PIX_FMT_RGB24);
        }

        return software_convert_for_ai(decoded_frame, dst);
    }

    bool convert_h264_h265_for_ai(const Frame& src, Frame& dst) {
        // H264/H265格式通过GStreamer appsrc解码后，通过RGA缩放
        Frame decoded_frame;

        // 1. GStreamer解码
        bool decode_success = false;
        if (gst_codec_) {
            if (src.format == V4L2_PIX_FMT_H264) {
                decode_success = gst_codec_->decode_h264(src, decoded_frame);
            } else if (src.format == V4L2_PIX_FMT_H265) {
                decode_success = gst_codec_->decode_h265(src, decoded_frame);
            }
        }

        if (!decode_success) {
            LOG_E("Failed to decode H264/H265 frame");
            return false;
        }

        // 2. RGA缩放到目标尺寸和格式
        if (rga_accel_) {
            return rga_accel_->convert_and_scale(decoded_frame, dst, config_.ai_width, config_.ai_height, V4L2_PIX_FMT_RGB24);
        }

        return software_convert_for_ai(decoded_frame, dst);
    }

    // Cloud路径的具体转换方法
    bool convert_h264_h265_for_cloud(const Frame& src, Frame& dst) {
        // H264/H265格式无需转换直接给cloud streaming
        dst = src;  // 直接复制
        return true;
    }

    bool convert_yuv_rgb_for_cloud(const Frame& src, Frame& dst) {
        // YUV/RGB格式通过GStreamer appsrc编码后送给cloud streaming
        if (gst_codec_) {
            return gst_codec_->encode_h264(src, dst, config_.cloud_bitrate);
        }

        return software_encode_h264(src, dst);
    }

    bool convert_mjpeg_for_cloud(const Frame& src, Frame& dst) {
        // MJPEG格式通过GStreamer appsrc编码后送给cloud streaming
        if (gst_codec_) {
            return gst_codec_->encode_h264(src, dst, config_.cloud_bitrate);
        }

        return software_encode_h264(src, dst);
    }

    // 软件fallback方法
    bool software_convert_for_ai(const Frame& src, Frame& dst) {
        // 简化的软件转换实现
        LOG_W("Using software conversion fallback for AI (not optimized)");

        // 这里应该实现软件的格式转换和缩放
        // 为了简化，我们只是复制数据并设置目标格式
        dst.data = src.data;
        dst.width = config_.ai_width;
        dst.height = config_.ai_height;
        dst.format = V4L2_PIX_FMT_RGB24;

        return true;
    }

    bool software_encode_h264(const Frame& src, Frame& dst) {
        // 简化的软件H264编码实现
        LOG_W("Using software H264 encoding fallback (not optimized)");

        // 这里应该使用x264库进行编码
        // 为了简化，我们只是复制数据并标记为H264
        dst.data = src.data;
        dst.format = V4L2_PIX_FMT_H264;
        dst.width = src.width;
        dst.height = src.height;

        return true;
    }
};

#endif // VIDEO_CONVERTER_H
