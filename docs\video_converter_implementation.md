# Video Converter 完整实现文档

## 概述

video_converter 服务已经按照用户需求完整实现，支持多种输入格式的双路径处理：
- **AI路径**: 转换为640x640 RGB888格式
- **Cloud路径**: 转换为H264/H265 NALU格式

## 支持的输入格式

1. **YUV格式** (YUYV, UYVY)
2. **RGB格式** (RGB24, BGR24)  
3. **MJPEG格式**
4. **H264格式**
5. **H265格式**

## 处理流程

### AI路径处理

1. **YUV/RGB格式**:
   - 使用RGA硬件加速进行格式转换和缩放到640x640 RGB888
   - 软件fallback: 使用OpenCV进行转换

2. **MJPEG格式**:
   - 使用MPP硬件解码器解码MJPEG
   - 使用RGA硬件加速缩放到640x640 RGB888
   - 软件fallback: 使用OpenCV进行处理

3. **H264/H265格式**:
   - 使用GStreamer appsrc解码
   - 使用RGA硬件加速缩放到640x640 RGB888
   - 软件fallback: 使用OpenCV进行处理

### Cloud路径处理

1. **H264/H265格式**:
   - 无需转换，直接传递给cloud streaming

2. **YUV/RGB格式**:
   - 使用GStreamer appsrc编码为H264
   - 支持硬件编码器(mpph264enc/mpph265enc)
   - 软件fallback: 使用x264编码器

3. **MJPEG格式**:
   - 使用GStreamer appsrc编码为H264
   - 支持硬件编码器
   - 软件fallback: 使用x264编码器

## 核心组件

### 1. RGAAccelerator类
- **功能**: 硬件加速的图像格式转换和缩放
- **依赖**: rga_utils.h, OpenCV
- **主要方法**:
  - `convert_and_scale()`: 转换格式并缩放到目标尺寸
  - `frame_to_mat()`: Frame转换为cv::Mat
  - `mat_to_frame()`: cv::Mat转换为Frame

### 2. MPPDecoder类
- **功能**: 硬件加速的MJPEG解码
- **依赖**: Rockchip MPP库
- **主要方法**:
  - `decode_mjpeg()`: 解码MJPEG数据
  - `get_decoded_frame()`: 获取解码后的帧数据

### 3. GStreamerCodec类
- **功能**: 视频编解码
- **依赖**: GStreamer库
- **主要方法**:
  - `encode_h264()`: H264编码
  - `encode_h265()`: H265编码
  - `decode_h264()`: H264解码
  - `decode_h265()`: H265解码

### 4. VideoConverter类
- **功能**: 主要的转换服务
- **特性**:
  - 多线程处理(ThreadPool)
  - DDS通信接口
  - 性能监控和统计
  - 配置文件支持

## 配置参数

配置文件: `config/video_converter.json`

主要参数:
- `enable_hardware_acceleration`: 启用硬件加速
- `ai_width/ai_height`: AI输出尺寸(默认640x640)
- `cloud_bitrate`: 云端输出码率(默认2Mbps)
- `thread_pool_size`: 线程池大小
- `stats_interval_sec`: 统计信息输出间隔

## DDS通信

- **输入Topic**: `Video_Frames` (可配置)
- **AI输出Topic**: `AI_Frames` (可配置)
- **Cloud输出Topic**: `Cloud_Frames` (可配置)

## 性能优化

1. **硬件加速优先**: 优先使用RGA、MPP、硬件编码器
2. **软件fallback**: 硬件不可用时自动切换到软件实现
3. **多线程处理**: AI和Cloud路径并行处理
4. **零拷贝优化**: 尽可能减少数据拷贝
5. **内存池**: 复用缓冲区减少内存分配

## 编译依赖

必需依赖:
- FastDDS (DDS通信)
- GStreamer (编解码)
- JsonCpp (配置文件)

可选依赖:
- Rockchip RGA (硬件加速)
- Rockchip MPP (硬件解码)
- OpenCV (软件fallback)

编译标志:
- `-DHAVE_RGA`: 启用RGA支持
- `-DHAVE_RKMPP`: 启用MPP支持
- `-DHAVE_OPENCV`: 启用OpenCV支持

## 使用方法

### 命令行启动
```bash
./video_converter \
  --config config/video_converter.json \
  --input-topic Video_Frames \
  --ai-topic AI_Frames \
  --cloud-topic Cloud_Frames \
  --verbose
```

### 编程接口
```cpp
VideoConverter converter;
VideoConverterConfig config;
// 设置配置...
converter.set_config(config);
converter.init("Video_Frames", "AI_Frames", "Cloud_Frames");
converter.start();
// 运行...
converter.stop();
```

## 测试

测试文件: `test/test_video_converter.cpp`

包含的测试:
- RGA加速器测试
- MPP解码器测试  
- GStreamer编解码测试
- VideoConverter基本功能测试

运行测试:
```bash
cd build
cmake --build . --target test_video_converter
./test_video_converter
```

## 故障排除

1. **RGA初始化失败**: 检查RGA库是否正确安装
2. **MPP解码失败**: 检查MPP库和驱动是否正确安装
3. **GStreamer错误**: 检查GStreamer插件是否完整安装
4. **DDS连接失败**: 检查FastDDS配置和网络连接

## 性能监控

VideoConverter提供实时统计信息:
- `frames_processed`: 处理的帧数
- `frames_dropped`: 丢弃的帧数
- `ai_frames_sent`: 发送给AI的帧数
- `cloud_frames_sent`: 发送给Cloud的帧数
- `cpu_usage`: CPU使用率

## 扩展性

系统设计支持:
- 新的输入格式添加
- 新的硬件加速器集成
- 自定义处理pipeline
- 动态配置更新
